<?xml version="1.0" encoding="UTF-8"?>
<config>
    <input>
        <jar in="target/777-AntyLogOut-1.1.1.jar" out="target/777-AntyLogOut-1.1.1-obf.jar"/>
    </input>
    
    <keep-names>
        <!-- Main plugin class - must be preserved for Bukkit -->
        <class access="public" name="me.darkness.antylogout.Main">
            <method access="public" name="onEnable"/>
            <method access="public" name="onDisable"/>
            <method access="public" name="onLoad"/>
        </class>
        
        <!-- Command executors - preserve execute methods -->
        <class access="public" name="me.darkness.antylogout.commands.**">
            <method access="public" name="onCommand"/>
            <method access="public" name="onTabComplete"/>
        </class>
        
        <!-- Event listeners - preserve event handler methods -->
        <class access="public" name="me.darkness.antylogout.combat.CombatEventListener">
            <method access="public" name="*"/>
        </class>
        <class access="public" name="me.darkness.antylogout.region.RegionEventListener">
            <method access="public" name="*"/>
        </class>
        
        <!-- API classes - if you want to expose them to other plugins -->
        <class access="public" name="me.darkness.antylogout.api.**">
            <method access="public" name="*"/>
        </class>
    </keep-names>
    
    <ignore-classes>
        <!-- Bukkit/Spigot/Paper APIs -->
        <class template="class org.bukkit.**"/>
        <class template="class org.spigotmc.**"/>
        <class template="class net.md_5.**"/>
        <class template="class io.papermc.**"/>
        
        <!-- WorldGuard API -->
        <class template="class com.sk89q.**"/>
        
        <!-- Java standard libraries -->
        <class template="class java.**"/>
        <class template="class javax.**"/>
        <class template="class sun.**"/>
        <class template="class com.sun.**"/>
        
        <!-- Common libraries -->
        <class template="class com.google.**"/>
        <class template="class lombok.**"/>
        <class template="class org.apache.**"/>
        <class template="class org.slf4j.**"/>
    </ignore-classes>
    
    <!-- Logging configuration -->
    <property name="log-file" value="target/allatori.log"/>
    
    <!-- Random seed for consistent obfuscation -->
    <property name="random-seed" value="777"/>
</config>
