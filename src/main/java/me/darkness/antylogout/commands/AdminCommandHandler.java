package me.darkness.antylogout.commands;

import me.darkness.antylogout.Main;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;

public class Admin<PERSON>ommand<PERSON>andler implements CommandExecutor {
    private final Main plugin;

    public AdminCommandHandler(Main plugin) {
        this.plugin = plugin;
    }

    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("777.antylogout.admin")) {
            sender.sendMessage(this.plugin.getConfigManager().getNoPermissionMessage());
            return true;
        } else if (args.length != 1) {
            sender.sendMessage(this.plugin.getConfigManager().getInvalidUsageMessage());
            return true;
        } else {
            switch (args[0].toLowerCase()) {
                case "off":
                    this.plugin.setPluginActive(false);
                    this.plugin.getCombatService().clearAllCombats();
                    sender.sendMessage(this.plugin.getConfigManager().getPluginDisabledMessage());
                    break;
                case "on":
                    this.plugin.setPluginActive(true);
                    sender.sendMessage(this.plugin.getConfigManager().getPluginEnabledMessage());
                    break;
                case "reload":
                    this.plugin.getConfigManager().reload();
                    this.plugin.getMessageService().clearAllBossBars();
                    sender.sendMessage(this.plugin.getConfigManager().getConfigReloadedMessage());
                    break;
                default:
                    sender.sendMessage(this.plugin.getConfigManager().getUnknownOptionMessage());
            }

            return true;
        }
    }
}
