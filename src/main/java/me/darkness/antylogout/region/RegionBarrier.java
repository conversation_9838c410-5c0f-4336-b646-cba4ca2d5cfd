package me.darkness.antylogout.region;

import com.sk89q.worldedit.math.BlockVector3;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import java.util.HashSet;
import java.util.Set;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;

public class RegionBarrier {
    private final Player player;
    private Location centerLocation;
    private final ProtectedRegion protectedRegion;
    private double distanceToRegion;
    private final Set<Location> currentGlassBlocks = new HashSet<>();
    private Set<Location> previousGlassBlocks = new HashSet<>();

    public RegionBarrier(Player player, Location center, ProtectedRegion region, double distance) {
        this.player = player;
        this.centerLocation = center.clone();
        this.protectedRegion = region;
        this.distanceToRegion = distance;
        this.updateGlassBlocks();
        this.show();
    }

    public void update(Location newCenter, ProtectedRegion region, double distance) {
        this.centerLocation = newCenter.clone();
        this.distanceToRegion = distance;
        this.updateGlassBlocks();
    }

    public void updateGlassBlocks() {
        Set<Location> blocksToRemove = new HashSet<>(this.currentGlassBlocks);
        this.currentGlassBlocks.clear();

        BlockVector3 regionMin = this.protectedRegion.getMinimumPoint();
        BlockVector3 regionMax = this.protectedRegion.getMaximumPoint();

        int minX = regionMin.getBlockX();
        int maxX = regionMax.getBlockX();
        int minZ = regionMin.getBlockZ();
        int maxZ = regionMax.getBlockZ();
        int playerY = this.centerLocation.getBlockY();
        int playerX = this.centerLocation.getBlockX();
        int playerZ = this.centerLocation.getBlockZ();

        int barrierWidth = Math.max(1, (int)Math.ceil(1.0 + (5.0 - this.distanceToRegion) * 2.0));
        int halfWidth = barrierWidth / 2;

        int startX = Math.max(minX, playerX - halfWidth);
        int endX = Math.min(maxX, playerX + halfWidth);
        int startZ = Math.max(minZ, playerZ - halfWidth);
        int endZ = Math.min(maxZ, playerZ + halfWidth);

        if (startZ == minZ) {
            this.createGlassWall(startX, endX, startZ, playerY);
        }
        if (endZ == maxZ) {
            this.createGlassWall(startX, endX, endZ, playerY);
        }
        if (startX == minX) {
            this.createGlassWallZ(startZ, endZ, startX, playerY, minZ, maxZ);
        }
        if (endX == maxX) {
            this.createGlassWallZ(startZ, endZ, endX, playerY, minZ, maxZ);
        }

        for (Location blockLocation : blocksToRemove) {
            if (!this.currentGlassBlocks.contains(blockLocation)) {
                this.player.sendBlockChange(blockLocation, blockLocation.getBlock().getBlockData());
            }
        }

        for (Location blockLocation : this.currentGlassBlocks) {
            this.player.sendBlockChange(blockLocation, Material.RED_STAINED_GLASS.createBlockData());
        }

        this.previousGlassBlocks = new HashSet<>(this.currentGlassBlocks);
    }

    private void createGlassWall(int startX, int endX, int z, int baseY) {
        for (int x = startX; x <= endX; x++) {
            for (int y = 0; y <= 5; y++) {
                Location blockLocation = new Location(this.centerLocation.getWorld(), x, baseY + y, z);
                if (y != 0 || blockLocation.getBlock().getType() == Material.AIR) {
                    this.currentGlassBlocks.add(blockLocation);
                }
            }
        }
    }

    private void createGlassWallZ(int startZ, int endZ, int x, int baseY, int regionMinZ, int regionMaxZ) {
        for (int z = startZ; z <= endZ; z++) {
            for (int y = 0; y <= 5; y++) {
                Location blockLocation = new Location(this.centerLocation.getWorld(), x, baseY + y, z);
                boolean isValidBlock = (y != 0 || blockLocation.getBlock().getType() == Material.AIR) &&
                                     (z != startZ || startZ != regionMinZ) &&
                                     (z != endZ || endZ != regionMaxZ);
                if (isValidBlock) {
                    this.currentGlassBlocks.add(blockLocation);
                }
            }
        }
    }

    public void show() {
        for (Location blockLocation : this.currentGlassBlocks) {
            this.player.sendBlockChange(blockLocation, Material.RED_STAINED_GLASS.createBlockData());
        }
        this.previousGlassBlocks = new HashSet<>(this.currentGlassBlocks);
    }

    public void hide() {
        if (this.player != null && this.player.isOnline()) {
            for (Location blockLocation : this.previousGlassBlocks) {
                this.player.sendBlockChange(blockLocation, blockLocation.getBlock().getBlockData());
            }

            for (Location blockLocation : this.currentGlassBlocks) {
                this.player.sendBlockChange(blockLocation, blockLocation.getBlock().getBlockData());
            }

            this.previousGlassBlocks.clear();
            this.currentGlassBlocks.clear();
        }
    }

    public Location getCenter() {
        return this.centerLocation;
    }

    public ProtectedRegion getRegion() {
        return this.protectedRegion;
    }

    public double getDistance() {
        return this.distanceToRegion;
    }
}
