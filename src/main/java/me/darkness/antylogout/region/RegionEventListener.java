package me.darkness.antylogout.region;

import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.math.BlockVector3;
import com.sk89q.worldedit.world.World;
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import com.sk89q.worldguard.protection.regions.RegionQuery;
import me.darkness.antylogout.Main;
import me.darkness.antylogout.utils.KnockbackHandler;
import me.darkness.antylogout.utils.RegionUtils;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

public class RegionEventListener implements Listener {
    private final Main plugin;

    public RegionEventListener(Main plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();

        if (!this.plugin.getCombatService().isInCombat(player) || !this.plugin.isPluginActive()) {
            this.plugin.getRegionService().removeBarrier(player);
            return;
        }

        Location targetLocation = event.getTo();
        Location currentLocation = event.getFrom();

        if (targetLocation == null) return;

        boolean playerMoved = targetLocation.getX() != currentLocation.getX() ||
                             targetLocation.getZ() != currentLocation.getZ() ||
                             targetLocation.getY() != currentLocation.getY();

        if (!playerMoved) return;

        World worldEditWorld = BukkitAdapter.adapt(targetLocation.getWorld());
        RegionManager regionManager = WorldGuard.getInstance().getPlatform().getRegionContainer().get(worldEditWorld);

        if (regionManager == null) {
            this.plugin.getRegionService().removeBarrier(player);
            return;
        }

        boolean enteringBlockedRegion = RegionUtils.isInBlockedRegion(targetLocation, this.plugin);
        boolean enteringPvpDisabledRegion = RegionUtils.isPvpDisabled(targetLocation);
        boolean enteringPvpZone = hasAnyPvpAllowRegion(targetLocation);
        boolean currentlyInPvpZone = hasAnyPvpAllowRegion(currentLocation);

        boolean tryingToEscapeAfterAttack = this.plugin.getCombatTracker()
            .isPlayerTryingToEscapeAfterAttack(player, targetLocation);

        if ((enteringBlockedRegion && !enteringPvpZone && !currentlyInPvpZone) ||
            (enteringPvpDisabledRegion && !enteringPvpZone) ||
            (tryingToEscapeAfterAttack && (enteringBlockedRegion || enteringPvpDisabledRegion))) {

            event.setCancelled(true);
            this.plugin.getRegionService().sendCooldownMessage(player);

            if (tryingToEscapeAfterAttack) {
                KnockbackHandler.pushPlayerBackHard(player, currentLocation, targetLocation);
                this.plugin.getRegionService().removeBarrier(player);
                player.sendMessage(this.plugin.getConfigManager().getEscapeAttemptBlockedMessage());
            } else {
                RegionProtectionType protectionType = this.plugin.getConfigManager().getRegionProtectionType();

                if (protectionType.isKnock()) {
                    KnockbackHandler.pushPlayerBackKnock(player, currentLocation, targetLocation);
                    this.plugin.getRegionService().removeBarrier(player);
                } else {
                    KnockbackHandler.pushPlayerBack(player, currentLocation, targetLocation);
                    this.plugin.getRegionService().removeBarrier(player);
                }
            }
        } else {
            RegionProtectionType protectionType = this.plugin.getConfigManager().getRegionProtectionType();
            if (protectionType.isBarrier()) {
                this.updateBarrier(player, targetLocation);
            } else {
                this.plugin.getRegionService().removeBarrier(player);
            }
        }
    }

    private void updateBarrier(Player player, Location playerLocation) {
        World worldEditWorld = BukkitAdapter.adapt(playerLocation.getWorld());
        RegionManager regionManager = WorldGuard.getInstance().getPlatform().getRegionContainer().get(worldEditWorld);

        if (regionManager == null) {
            this.plugin.getRegionService().removeBarrier(player);
            return;
        }

        ProtectedRegion nearestRegion = null;
        double shortestDistance = Double.MAX_VALUE;
        BlockVector3 nearestEdgePoint = null;
        double maxBarrierDistance = 5.0;

        boolean playerInPvpZone = hasAnyPvpAllowRegion(playerLocation);

        for (ProtectedRegion region : regionManager.getRegions().values()) {
            boolean shouldCreateBarrier = false;

            if (playerInPvpZone) {
                boolean isBlockedRegion = this.plugin.getConfigManager().getBlockedRegions().contains(region.getId());
                boolean isPvpDisabledRegion = region.getFlag(Flags.PVP) == StateFlag.State.DENY;
                boolean isPvpAllowRegion = region.getFlag(Flags.PVP) == StateFlag.State.ALLOW;

                shouldCreateBarrier = (isBlockedRegion || isPvpDisabledRegion) && !isPvpAllowRegion;
            } else {
                shouldCreateBarrier = this.plugin.getConfigManager().getBlockedRegions().contains(region.getId());
            }

            if (!shouldCreateBarrier) {
                continue;
            }

            BlockVector3 regionMin = region.getMinimumPoint();
            BlockVector3 regionMax = region.getMaximumPoint();

            double nearestX = Math.max(regionMin.getX(), Math.min(playerLocation.getX(), regionMax.getX()));
            double nearestZ = Math.max(regionMin.getZ(), Math.min(playerLocation.getZ(), regionMax.getZ()));

            Location regionEdgePoint = new Location(playerLocation.getWorld(), nearestX, playerLocation.getY(), nearestZ);
            double distanceToRegion = playerLocation.distance(regionEdgePoint);

            if (distanceToRegion < shortestDistance && distanceToRegion <= maxBarrierDistance) {
                shortestDistance = distanceToRegion;
                nearestRegion = region;
                nearestEdgePoint = BlockVector3.at(nearestX, playerLocation.getY(), nearestZ);
            }
        }

        if (nearestRegion != null && nearestEdgePoint != null) {
            Location barrierLocation = new Location(playerLocation.getWorld(),
                    nearestEdgePoint.getX(), playerLocation.getY(), nearestEdgePoint.getZ());
            RegionBarrier barrier = new RegionBarrier(player, barrierLocation, nearestRegion, shortestDistance);
            this.plugin.getRegionService().showBarrier(player, barrier);
        } else {
            this.plugin.getRegionService().removeBarrier(player);
        }
    }

    private boolean hasAnyPvpAllowRegion(Location location) {
        com.sk89q.worldedit.util.Location weLoc = BukkitAdapter.adapt(location);
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        ApplicableRegionSet regions = query.getApplicableRegions(weLoc);

        for (var region : regions.getRegions()) {
            StateFlag.State regionPvpState = region.getFlag(Flags.PVP);
            if (regionPvpState == StateFlag.State.ALLOW) {
                return true;
            }
        }
        return false;
    }
}
