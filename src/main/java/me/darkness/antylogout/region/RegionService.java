package me.darkness.antylogout.region;

import me.darkness.antylogout.Main;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class RegionService {
    private final Main core;
    private final Map<UUID, RegionBarrier> barriers = new ConcurrentHashMap<>();
    private final Map<UUID, Long> msgCooldowns = new ConcurrentHashMap<>();
    private static final long MSG_COOLDOWN = 2000;
    private static final long BARRIER_UPDATE_TICKS = 4L;

    public RegionService(Main core) {
        this.core = core;
    }

    public void removeBarrier(Player p) {
        if (p == null) return;

        UUID id = p.getUniqueId();
        RegionBarrier barrier = barriers.remove(id);
        if (barrier != null) {
            if (core.getServer().isPrimaryThread()) {
                barrier.hide();
            } else {
                // Only schedule task if plugin is still enabled
                if (core.isEnabled()) {
                    core.getServer().getScheduler().runTask(core, () -> barrier.hide());
                } else {
                    // If plugin is being disabled, hide barrier directly (might cause issues but better than crash)
                    barrier.hide();
                }
            }
        }
    }

    public void forceRemoveBarrier(Player p) {
        if (p == null) return;

        UUID id = p.getUniqueId();
        RegionBarrier barrier = barriers.remove(id);
        if (barrier != null) {
            if (core.getServer().isPrimaryThread()) {
                barrier.hide();
            } else {
                // Only schedule task if plugin is still enabled
                if (core.isEnabled()) {
                    core.getServer().getScheduler().runTask(core, () -> {
                        barrier.hide();
                    });
                } else {
                    // If plugin is being disabled, hide barrier directly (might cause issues but better than crash)
                    barrier.hide();
                }
            }
        }
    }

    public void sendCooldownMessage(Player p) {
        if (p == null) return;

        long now = System.currentTimeMillis();
        UUID id = p.getUniqueId();

        if (!msgCooldowns.containsKey(id) || now - msgCooldowns.get(id) >= MSG_COOLDOWN) {
            p.sendMessage(core.getConfigManager().getCannotEnterRegionMessage());
            msgCooldowns.put(id, now);
        }
    }

    public void showBarrier(Player p, RegionBarrier barrier) {
        if (p == null || barrier == null) return;

        UUID id = p.getUniqueId();
        if (barriers.containsKey(id)) {
            barriers.get(id).update(barrier.getCenter(), barrier.getRegion(), barrier.getDistance());
        } else {
            barriers.put(id, barrier);
            startBarrierUpdater(p);
        }
    }

    private void startBarrierUpdater(Player p) {
        if (p == null) return;

        new BukkitRunnable() {
            private final UUID playerId = p.getUniqueId();
            private int checkCounter = 0;

            @Override
            public void run() {
                if (!barriers.containsKey(playerId) || !p.isOnline() || !core.isPluginActive()) {
                    cancel();
                    forceRemoveBarrier(p);
                    return;
                }

                if (!core.getCombatService().isInCombat(p)) {
                    cancel();
                    forceRemoveBarrier(p);
                    return;
                }

                RegionBarrier currentBarrier = barriers.get(playerId);
                if (currentBarrier != null) {
                    currentBarrier.updateGlassBlocks();
                }

                checkCounter++;
                if (checkCounter >= 5) {
                    checkCounter = 0;
                    if (!core.getCombatService().isInCombat(p)) {
                        cancel();
                        forceRemoveBarrier(p);
                    }
                }
            }
        }.runTaskTimer(core, 0L, BARRIER_UPDATE_TICKS);
    }
}