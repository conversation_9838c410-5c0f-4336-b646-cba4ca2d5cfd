package me.darkness.antylogout.region;

public enum RegionProtectionType {
    BARRIER,
    KNOCK;

    public static RegionProtectionType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BARRIER;
        }
        
        try {
            return valueOf(value.toUpperCase().trim());
        } catch (IllegalArgumentException e) {
            return BARRIER;
        }
    }

    public boolean isBarrier() {
        return this == BARRIER;
    }

    public boolean isKnock() {
        return this == KNOCK;
    }
}
