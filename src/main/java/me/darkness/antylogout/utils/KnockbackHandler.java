package me.darkness.antylogout.utils;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class KnockbackHandler {

    public KnockbackHandler() {
    }

    public static void pushPlayerBack(Player player, Location from, Location to) {
        pushPlayerBackStandard(player, from, to);
    }

    public static void pushPlayerBackStandard(Player player, Location from, Location to) {
        if (player == null || from == null || to == null) {
            return;
        }

        double dx = from.getX() - to.getX();
        double dz = from.getZ() - to.getZ();
        double magnitude = Math.sqrt(dx * dx + dz * dz);

        if (magnitude > 0.0) {
            Vector knockback = new Vector(dx / magnitude, 0.0, dz / magnitude)
                .multiply(1.2);

            knockback.setY(0.4);

            player.setVelocity(new Vector(0, 0, 0));

            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("777-AntyLogOut"),
                () -> player.setVelocity(knockback),
                1L
            );
        }
    }
    public static void pushPlayerBackKnock(Player player, Location from, Location to) {
        pushPlayerBackStandard(player, from, to);
    }

    public static void pushPlayerBackHard(Player player, Location from, Location to) {
        if (player == null || from == null || to == null) {
            return;
        }

        double dx = from.getX() - to.getX();
        double dz = from.getZ() - to.getZ();
        double magnitude = Math.sqrt(dx * dx + dz * dz);

        if (magnitude > 0.0) {
            Vector knockback = new Vector(dx / magnitude, 0.0, dz / magnitude)
                .multiply(1.6);
            knockback.setY(0.5);

            player.setVelocity(new Vector(0, 0, 0));

            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("777-AntyLogOut"),
                () -> player.setVelocity(knockback),
                1L
            );
        }
    }
}
