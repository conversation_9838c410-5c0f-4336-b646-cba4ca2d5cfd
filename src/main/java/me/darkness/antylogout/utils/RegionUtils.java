package me.darkness.antylogout.utils;

import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.regions.RegionQuery;
import me.darkness.antylogout.Main;
import org.bukkit.Location;
import org.bukkit.entity.Player;

public class RegionUtils {

    public static boolean isInBlockedRegion(Location location, Main plugin) {
        com.sk89q.worldedit.util.Location weLoc = BukkitAdapter.adapt(location);
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        return query.getApplicableRegions(weLoc).getRegions().stream()
                .anyMatch(region -> plugin.getConfigManager().getBlockedRegions().contains(region.getId()));
    }

    public static boolean isPvpDisabled(Location location) {
        com.sk89q.worldedit.util.Location weLoc = BukkitAdapter.adapt(location);
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        ApplicableRegionSet regions = query.getApplicableRegions(weLoc);

        for (var region : regions.getRegions()) {
            StateFlag.State regionPvpState = region.getFlag(Flags.PVP);
            if (regionPvpState == StateFlag.State.ALLOW) {
                return false;
            }
        }

        StateFlag.State pvpState = regions.queryState(null, Flags.PVP);

        if (pvpState == StateFlag.State.DENY) {
            return true;
        }

        if (pvpState == StateFlag.State.ALLOW) {
            return false;
        }

        return !location.getWorld().getPVP();
    }

    public static boolean isPvpDisabledForPlayer(Player player) {
        com.sk89q.worldedit.util.Location weLoc = BukkitAdapter.adapt(player.getLocation());
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        ApplicableRegionSet regions = query.getApplicableRegions(weLoc);

        for (var region : regions.getRegions()) {
            StateFlag.State regionPvpState = region.getFlag(Flags.PVP);
            if (regionPvpState == StateFlag.State.ALLOW) {
                return false;
            }
        }

        com.sk89q.worldguard.LocalPlayer localPlayer = WorldGuardPlugin.inst().wrapPlayer(player);
        StateFlag.State pvpState = regions.queryState(localPlayer, Flags.PVP);

        if (pvpState == StateFlag.State.DENY) {
            return true;
        }

        if (pvpState == StateFlag.State.ALLOW) {
            return false;
        }

        boolean globalPvpDisabled = isPvpDisabledGlobally(player);
        return globalPvpDisabled;
    }

    public static boolean isPvpDisabledGlobally(Player player) {
        return !player.getWorld().getPVP();
    }

    public static boolean isPvpDisabledBetweenPlayers(Player player1, Player player2) {
        return isPvpDisabledForPlayer(player1) || isPvpDisabledForPlayer(player2);
    }
}
