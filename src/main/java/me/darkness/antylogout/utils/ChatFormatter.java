package me.darkness.antylogout.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import net.md_5.bungee.api.ChatColor;

public class ChatFormatter {
    private static final Pattern HEX_PATTERN = Pattern.compile("&#([0-9A-Fa-f]{6})");

    private ChatFormatter() {
    }

    public static String color(String message) {
        if (message == null || message.isEmpty()) {
            return "";
        }
        
        Matcher matcher = HEX_PATTERN.matcher(message);
        if (!matcher.find()) {
            return ChatColor.translateAlternateColorCodes('&', message);
        }
        
        matcher.reset();
        StringBuilder buffer = new StringBuilder(message.length() + 16);
        while (matcher.find()) {
            String hex = matcher.group(1);
            matcher.appendReplacement(buffer, ChatColor.of("#" + hex).toString());
        }
        matcher.appendTail(buffer);
        return ChatColor.translateAlternateColorCodes('&', buffer.toString());
    }
}
