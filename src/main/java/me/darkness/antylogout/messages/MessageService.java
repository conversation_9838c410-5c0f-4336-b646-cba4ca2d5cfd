package me.darkness.antylogout.messages;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import me.darkness.antylogout.Main;
import me.darkness.antylogout.utils.ChatFormatter;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.Player;

public class MessageService {
    private final Main plugin;
    private final Map<UUID, BossBar> activeBossBars = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> lastTimerSecond = new ConcurrentHashMap<>();

    public MessageService(Main plugin) {
        this.plugin = plugin;
    }

    public void sendCombatStart(Player player) {
        int combatDuration = plugin.getConfigManager().getCombatDuration();
        sendMessages(player, plugin.getConfigManager().getCombatStartMessages(), "%seconds%", String.valueOf(combatDuration));
        if (plugin.getConfigManager().isBossBarEnabled()) {
            setupBossBar(player, combatDuration);
        }
    }

    public void sendCombatEnd(Player player) {
        sendMessages(player, plugin.getConfigManager().getCombatEndMessages(), null, null);
        clearBossBar(player);
        lastTimerSecond.remove(player.getUniqueId());
    }

    public void sendCombatTimer(Player player, int seconds) {
        updateTimerDisplay(player, plugin.getConfigManager().getCombatStartMessages(), "%seconds%", String.valueOf(seconds));
        if (plugin.getConfigManager().isBossBarEnabled()) {
            updateBossBar(player, seconds);
        }
    }

    public void sendLogoutDuringCombat(Player player) {
        String message = plugin.getConfigManager().getLogoutDuringCombatMessage().replace("%player%", player.getName());
        plugin.getServer().broadcastMessage(message);
        clearBossBar(player);
        lastTimerSecond.remove(player.getUniqueId());
    }

    public void clearAllBossBars() {
        activeBossBars.values().forEach(BossBar::removeAll);
        activeBossBars.clear();
        lastTimerSecond.clear();
    }

    private void sendMessages(Player player, Iterable<String> messages, String placeholder, String value) {
        for (String message : messages) {
            if (message != null && !message.isEmpty()) {
                String formattedMessage = placeholder != null ? message.replace(placeholder, value) : message;
                sendMessage(player, formattedMessage);
            }
        }
    }

    private void updateTimerDisplay(Player player, Iterable<String> messages, String placeholder, String value) {
        UUID playerId = player.getUniqueId();
        int currentSecond = Integer.parseInt(value);
        Integer lastSecond = lastTimerSecond.get(playerId);
        boolean secondChanged = lastSecond == null || !lastSecond.equals(currentSecond);
        
        for (String message : messages) {
            if (message != null && !message.isEmpty()) {
                String formattedMessage = placeholder != null ? message.replace(placeholder, value) : message;
                String[] parts = formattedMessage.split("]", 2);
                if (parts.length >= 2 && !parts[1].trim().isEmpty()) {
                    String type = parts[0].replace("[", "").toUpperCase();

                    if ("ACTIONBAR".equals(type) || "BOSSBAR".equals(type)) {
                        sendMessage(player, formattedMessage);
                    }
                    else if (secondChanged) {
                        sendMessage(player, formattedMessage);
                    }
                }
            }
        }

        if (secondChanged) {
            lastTimerSecond.put(playerId, currentSecond);
        }
    }

    private void sendMessage(Player player, String message) {
        String[] parts = message.split("]", 2);
        if (parts.length >= 2 && !parts[1].trim().isEmpty()) {
            String type = parts[0].replace("[", "").toUpperCase();
            String content = ChatFormatter.color(parts[1].trim());
            switch (type) {
                case "TITLE":
                    player.sendTitle(content, "", 10, 70, 20);
                    break;
                case "SUBTITLE":
                    player.sendTitle("", content, 10, 70, 20);
                    break;
                case "ACTIONBAR":
                    player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText(content));
                    break;
                case "BOSSBAR":
                    BossBar bar = activeBossBars.get(player.getUniqueId());
                    if (bar != null) {
                        bar.setTitle(content);
                    }
                    break;
                case "MSG":
                    player.sendMessage(content);
                    break;
                default:
                    plugin.getLogger().warning("nie ma wiadomosci: " + type);
            }
        }
    }

    private void setupBossBar(Player player, int maxSeconds) {
        UUID playerId = player.getUniqueId();
        clearBossBar(player);
        BossBar bar = Bukkit.createBossBar("", BarColor.RED, BarStyle.SOLID);
        bar.addPlayer(player);
        bar.setProgress(1.0);
        activeBossBars.put(playerId, bar);
    }

    private void updateBossBar(Player player, int seconds) {
        BossBar bar = activeBossBars.get(player.getUniqueId());
        if (bar != null) {
            double progress = (double) seconds / plugin.getConfigManager().getCombatDuration();
            bar.setProgress(Math.max(0.0, Math.min(1.0, progress)));
        }
    }

    private void clearBossBar(Player player) {
        UUID playerId = player.getUniqueId();
        BossBar bar = activeBossBars.remove(playerId);
        if (bar != null) {
            bar.removePlayer(player);
            bar.removeAll();
        }
    }
}
