package me.darkness.antylogout;

import me.darkness.antylogout.combat.CombatEventListener;
import me.darkness.antylogout.combat.CombatService;
import me.darkness.antylogout.combat.CombatTracker;
import me.darkness.antylogout.commands.AdminCommandHandler;
import me.darkness.antylogout.config.ConfigManager;
import me.darkness.antylogout.messages.MessageService;
import me.darkness.antylogout.region.RegionEventListener;
import me.darkness.antylogout.region.RegionService;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

public class Main extends JavaPlugin {
    private ConfigManager configManager;
    private CombatService combatService;
    private MessageService messageService;
    private RegionService regionService;
    private CombatTracker combatTracker;
    private boolean isPluginActive = true;

    public Main() {
    }

    public void onEnable() {
        this.configManager = new ConfigManager(this);
        getServer().getConsoleSender().sendMessage("§8[§a§l777-AntyLogOut§8] §f§nZaladowano konfiguracje komendy§r.");
        this.combatService = new CombatService(this);
        this.messageService = new MessageService(this);
        this.regionService = new RegionService(this);
        this.combatTracker = new CombatTracker();
        this.getServer().getPluginManager().registerEvents(new CombatEventListener(this), this);
        this.getServer().getPluginManager().registerEvents(new RegionEventListener(this), this);
        getServer().getConsoleSender().sendMessage("§8[§a§l777-AntyLogOut§8] §f§nZarejestrowano listenery§r.");
        this.getCommand("adminantylogout").setExecutor(new AdminCommandHandler(this));

        new BukkitRunnable() {
            @Override
            public void run() {
                if (combatTracker != null) {
                    combatTracker.cleanupOldAttacks();
                }
            }
        }.runTaskTimerAsynchronously(this, 600L, 600L); // 30 sekund = 600 ticków
        getServer().getConsoleSender().sendMessage("§8[§a§l777-AntyLogOut§8] §f§nZarejestrowano komendy§r.");

        getServer().getConsoleSender().sendMessage("§8[§a§l777-AntyLogOut§8] §fUruchomiono plugin :P");
    }

    public void onDisable() {
        if (isPluginActive) {
            combatService.clearAllCombats();
            messageService.clearAllBossBars();
        }
        getServer().getConsoleSender().sendMessage("§8[§4§l777-AntyLogOut§8] §cWyłączono plugin :C");
    }

    public ConfigManager getConfigManager() {
        return this.configManager;
    }

    public CombatService getCombatService() {
        return this.combatService;
    }

    public MessageService getMessageService() {
        return this.messageService;
    }

    public RegionService getRegionService() {
        return this.regionService;
    }

    public CombatTracker getCombatTracker() {
        return this.combatTracker;
    }

    public boolean isPluginActive() {
        return this.isPluginActive;
    }

    public void setPluginActive(boolean active) {
        this.isPluginActive = active;
    }
}