package me.darkness.antylogout.combat;

import me.darkness.antylogout.Main;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class CombatService {
    private final Main plugin;
    private final Map<UUID, Integer> activeCombats = new ConcurrentHashMap<>();
    private final Map<UUID, BukkitRunnable> combatTimers = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastMessageSent = new ConcurrentHashMap<>();

    public CombatService(Main plugin) {
        this.plugin = plugin;
    }

    public void startCombat(Player player) {
        if (player == null) return;

        UUID playerId = player.getUniqueId();
        cancelTimer(playerId);

        long currentTime = System.currentTimeMillis();
        Long lastMessageTime = lastMessageSent.get(playerId);
        if (lastMessageTime == null || currentTime - lastMessageTime > 1000) {
            plugin.getMessageService().sendCombatStart(player);
            lastMessageSent.put(playerId, currentTime);
        }

        int combatDuration = plugin.getConfigManager().getCombatDuration();
        activeCombats.put(playerId, combatDuration);

        BukkitRunnable combatTask = new BukkitRunnable() {
            int remainingTime = combatDuration;
            int tickCounter = 0;

            @Override
            public void run() {
                if (!activeCombats.containsKey(playerId) || !player.isOnline()) {
                    cancel();
                    return;
                }

                if (remainingTime <= 0) {
                    endCombat(player);
                    cancel();
                    return;
                }

                plugin.getMessageService().sendCombatTimer(player, remainingTime);

                tickCounter++;
                if (tickCounter >= 4) {
                    remainingTime--;
                    activeCombats.put(playerId, remainingTime);
                    tickCounter = 0;
                }
            }
        };

        combatTask.runTaskTimer(plugin, 0L, 5L);
        combatTimers.put(playerId, combatTask);
    }

    public void endCombat(Player player) {
        if (player == null) return;

        UUID playerId = player.getUniqueId();
        cancelTimer(playerId);
        activeCombats.remove(playerId);
        lastMessageSent.remove(playerId);

        if (player.isOnline()) {
            plugin.getMessageService().sendCombatEnd(player);
        }

        plugin.getRegionService().forceRemoveBarrier(player);
        plugin.getCombatTracker().clearAttackInfo(player);

        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            if (!isInCombat(player)) {
                plugin.getRegionService().forceRemoveBarrier(player);
            }
        }, 20L);
    }

    public void handleLogout(Player player) {
        if (player == null || !isInCombat(player)) return;

        plugin.getMessageService().sendLogoutDuringCombat(player);
        player.setHealth(0);
        endCombat(player);
    }

    public void clearAllCombats() {
        plugin.getServer().getOnlinePlayers().stream()
                .filter(this::isInCombat)
                .forEach(this::endCombat);

        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getServer().getOnlinePlayers().forEach(player ->
                plugin.getRegionService().forceRemoveBarrier(player));
        }, 20L);
    }

    public boolean isInCombat(Player player) {
        return player != null && activeCombats.containsKey(player.getUniqueId());
    }

    private void cancelTimer(UUID playerId) {
        BukkitRunnable timer = combatTimers.remove(playerId);
        if (timer != null) {
            timer.cancel();
        }
    }
}