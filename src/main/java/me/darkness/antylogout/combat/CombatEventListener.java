package me.darkness.antylogout.combat;

import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.regions.RegionQuery;
import me.darkness.antylogout.Main;
import me.darkness.antylogout.utils.KnockbackHandler;
import me.darkness.antylogout.utils.RegionUtils;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.EnderPearl;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Mob;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class CombatEventListener implements Listener {
    private final Main plugin;
    private final Map<UUID, Long> pearlMessageCooldowns = new ConcurrentHashMap<>();
    private static final long PEARL_MESSAGE_COOLDOWN = 2000;

    public CombatEventListener(Main plugin) {
        this.plugin = plugin;
    }

    private void sendCannotEnterRegionMessage(Player player) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        Long lastMessageTime = pearlMessageCooldowns.get(playerId);
        if (lastMessageTime == null || currentTime - lastMessageTime >= PEARL_MESSAGE_COOLDOWN) {
            player.sendMessage(this.plugin.getConfigManager().getCannotEnterRegionMessage());
            pearlMessageCooldowns.put(playerId, currentTime);
        }

        cleanupOldCooldowns();
    }

    private void cleanupOldCooldowns() {
        long currentTime = System.currentTimeMillis();
        pearlMessageCooldowns.entrySet().removeIf(entry ->
            currentTime - entry.getValue() > PEARL_MESSAGE_COOLDOWN * 2);
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageByEntityEvent event) {

        if (event.isCancelled()) {
            return;
        }

        if (event.getEntity() instanceof Player) {
            Player target = (Player)event.getEntity();
            Player attacker = this.getAttacker(event.getDamager());

            if (attacker == null && event.getDamager() instanceof Mob && this.plugin.getConfigManager().isMobCombatEnabled()) {
                if (!this.isInBlockedRegion(target) && !this.isNPC(event.getDamager()) && !RegionUtils.isPvpDisabledForPlayer(target)) {
                    if (!target.hasPermission("antylogout.bypass")) {
                        this.plugin.getCombatService().startCombat(target);
                    }
                }
            } else if (attacker != null && this.plugin.isPluginActive()) {
                if (attacker.getGameMode().getValue() == 1) {
                    return;
                }

                boolean targetInBlockedRegion = this.isInBlockedRegion(target);
                boolean attackerInBlockedRegion = this.isInBlockedRegion(attacker);
                boolean pvpDisabledBetweenPlayers = RegionUtils.isPvpDisabledBetweenPlayers(target, attacker);

                if (targetInBlockedRegion && !attackerInBlockedRegion) {
                    event.setCancelled(true);
                    return;
                }

                boolean targetInPvpZone = this.hasAnyPvpAllowRegion(target.getLocation());
                boolean attackerInPvpZone = this.hasAnyPvpAllowRegion(attacker.getLocation());

                boolean shouldStartCombat = (targetInPvpZone && attackerInPvpZone) ||
                    (!targetInBlockedRegion && !attackerInBlockedRegion && !pvpDisabledBetweenPlayers);

                if (shouldStartCombat && !this.isNPC(target) && !this.isNPC(attacker)) {
                    if (!target.hasPermission("antylogout.bypass")) {
                        this.plugin.getCombatService().startCombat(target);
                    }
                    if (!attacker.hasPermission("antylogout.bypass")) {
                        this.plugin.getCombatService().startCombat(attacker);
                        this.plugin.getCombatTracker().registerAttack(attacker, attacker.getLocation());
                    }
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageMonitor(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        Player target = (Player) event.getEntity();

        if (event.isCancelled() && this.plugin.getCombatService().isInCombat(target)) {
            if (event instanceof EntityDamageByEntityEvent) {
                EntityDamageByEntityEvent damageByEntityEvent = (EntityDamageByEntityEvent) event;
                Player attacker = this.getAttacker(damageByEntityEvent.getDamager());

                if (attacker != null) {
                    return;
                }
            }
        }
    }

    @EventHandler
    public void onPearlTeleport(PlayerTeleportEvent event) {
        if (event.getCause() == TeleportCause.ENDER_PEARL) {
            Player player = event.getPlayer();
            Location to = event.getTo();
            Location from = event.getFrom();
            if (to != null) {
                if (this.plugin.isPluginActive() && this.plugin.getCombatService().isInCombat(player)) {

                    boolean fromBlockedRegion = RegionUtils.isInBlockedRegion(from, this.plugin);
                    boolean toBlockedRegion = RegionUtils.isInBlockedRegion(to, this.plugin);

                    if (toBlockedRegion && !fromBlockedRegion) {
                        event.setCancelled(true);
                        sendCannotEnterRegionMessage(player);

                        if (this.plugin.getConfigManager().getRegionProtectionType().isKnock()) {
                            KnockbackHandler.pushPlayerBackKnock(player, from, to);
                        } else {
                            KnockbackHandler.pushPlayerBack(player, from, to);
                        }
                    } else {
                        this.plugin.getRegionService().forceRemoveBarrier(player);
                        if (this.plugin.getCombatService().isInCombat(player)) {
                            this.plugin.getServer().getScheduler().runTaskLater(this.plugin, () -> this.plugin.getServer().getPluginManager().callEvent(new PlayerMoveEvent(player, from, to)), 1L);
                        }
                    }
                }

            }
        }
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        if (this.plugin.isPluginActive() && this.plugin.getCombatService().isInCombat(player)) {
            if ((event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) && event.getItem() != null && event.getItem().getType() == Material.ENDER_PEARL) {
                if (this.plugin.getConfigManager().isEnderPearlCombatEnabled()) {
                    this.plugin.getCombatService().startCombat(player);
                }

                Location targetLoc = player.getTargetBlockExact(5) != null ? player.getTargetBlockExact(5).getLocation() : null;
                if (targetLoc != null && RegionUtils.isInBlockedRegion(targetLoc, this.plugin)) {
                    event.setCancelled(true);
                    sendCannotEnterRegionMessage(player);
                    return;
                }

                Location eyeLoc = player.getEyeLocation();
                Vector direction = eyeLoc.getDirection();
                Location currentLoc = eyeLoc.clone();

                for(int i = 0; i < 16; ++i) {
                    currentLoc.add(direction);
                    if (RegionUtils.isInBlockedRegion(currentLoc, this.plugin)) {
                        event.setCancelled(true);
                        sendCannotEnterRegionMessage(player);
                        return;
                    }
                }
            }

            if (event.getClickedBlock() != null && event.getClickedBlock().getType() == Material.RED_STAINED_GLASS) {
                event.setCancelled(true);
            } else {
                if (this.plugin.getConfigManager().isEnderChestBlocked()) {
                    if (event.getClickedBlock() != null && event.getClickedBlock().getType() == Material.ENDER_CHEST && event.getAction() == Action.RIGHT_CLICK_BLOCK) {
                        event.setCancelled(true);
                        player.sendMessage(this.plugin.getConfigManager().getCannotUseEnderChestMessage());
                        return;
                    }

                    ItemStack item = event.getItem();
                    if (item != null && item.getType() == Material.ENDER_CHEST) {
                        event.setCancelled(true);
                        player.sendMessage(this.plugin.getConfigManager().getCannotUseEnderChestMessage());
                    }
                }

            }
        }
    }

    @EventHandler
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player dead = event.getEntity();
        Player killer = dead.getKiller();
        if (this.plugin.getCombatService().isInCombat(dead)) {
            this.plugin.getCombatService().endCombat(dead);
        }

        if (killer != null && this.plugin.getCombatService().isInCombat(killer) && !this.isInBlockedRegion(killer)) {
            this.plugin.getCombatService().startCombat(killer);
        }

    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        if (this.plugin.getCombatService().isInCombat(player)) {
            this.plugin.getCombatService().handleLogout(player);
        }

        pearlMessageCooldowns.remove(player.getUniqueId());
    }

    @EventHandler
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        if (this.plugin.getCombatService().isInCombat(player) && this.plugin.isPluginActive()) {
            String command = event.getMessage().substring(1).split(" ")[0].toLowerCase();
            if (!this.plugin.getConfigManager().getWhitelistedCommands().contains(command)) {
                event.setCancelled(true);
                player.sendMessage(this.plugin.getConfigManager().getCannotUseCommandMessage());
            }

        }
    }

    private Player getAttacker(Entity damager) {
        if (damager instanceof Player) {
            return (Player)damager;
        } else {
            if (damager instanceof Projectile && this.plugin.getConfigManager().isProjectileCombatEnabled()) {
                Projectile projectile = (Projectile)damager;
                if (projectile instanceof EnderPearl && !this.plugin.getConfigManager().isEnderPearlCombatEnabled()) {
                    return null;
                }

                if (projectile.getShooter() instanceof Player) {
                    Player shooter = (Player)projectile.getShooter();
                    if (shooter.getGameMode().getValue() == 1) {
                        return null;
                    }
                    return shooter;
                }
            }

            return null;
        }
    }

    private boolean isInBlockedRegion(Player player) {
        com.sk89q.worldedit.util.Location loc = BukkitAdapter.adapt(player.getLocation());
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        ApplicableRegionSet regions = query.getApplicableRegions(loc);
        return regions.getRegions().stream().anyMatch((region) -> this.plugin.getConfigManager().getBlockedRegions().contains(region.getId()));
    }

    private boolean isNPC(Entity entity) {
        return entity.hasMetadata("NPC");
    }

    private boolean hasAnyPvpAllowRegion(Location location) {
        com.sk89q.worldedit.util.Location weLoc = BukkitAdapter.adapt(location);
        RegionQuery query = WorldGuard.getInstance().getPlatform().getRegionContainer().createQuery();
        ApplicableRegionSet regions = query.getApplicableRegions(weLoc);

        for (var region : regions.getRegions()) {
            StateFlag.State regionPvpState = region.getFlag(Flags.PVP);
            if (regionPvpState == StateFlag.State.ALLOW) {
                return true;
            }
        }
        return false;
    }
}
