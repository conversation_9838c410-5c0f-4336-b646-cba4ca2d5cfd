package me.darkness.antylogout.combat;

import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class CombatTracker {
    
    private final Map<UUID, AttackInfo> lastAttacks = new ConcurrentHashMap<>();
    private static final long ATTACK_MEMORY_TIME = 5000;

    public void registerAttack(Player attacker, Location attackLocation) {
        if (attacker == null || attackLocation == null) {
            return;
        }
        
        UUID attackerId = attacker.getUniqueId();
        AttackInfo attackInfo = new AttackInfo(attackLocation.clone(), System.currentTimeMillis());
        lastAttacks.put(attackerId, attackInfo);
    }

    public boolean isPlayerTryingToEscapeAfterAttack(Player player, Location currentLocation) {
        if (player == null || currentLocation == null) {
            return false;
        }
        
        UUID playerId = player.getUniqueId();
        AttackInfo lastAttack = lastAttacks.get(playerId);
        
        if (lastAttack == null) {
            return false;
        }
        
        long timeSinceAttack = System.currentTimeMillis() - lastAttack.timestamp;

        if (timeSinceAttack > ATTACK_MEMORY_TIME) {
            lastAttacks.remove(playerId);
            return false;
        }

        double distanceFromAttack = lastAttack.location.distance(currentLocation);

        return distanceFromAttack > 10.0;
    }

    public void clearAttackInfo(Player player) {
        if (player != null) {
            lastAttacks.remove(player.getUniqueId());
        }
    }

    public void cleanupOldAttacks() {
        long currentTime = System.currentTimeMillis();
        lastAttacks.entrySet().removeIf(entry -> 
            currentTime - entry.getValue().timestamp > ATTACK_MEMORY_TIME);
    }

    private static class AttackInfo {
        final Location location;
        final long timestamp;
        
        AttackInfo(Location location, long timestamp) {
            this.location = location;
            this.timestamp = timestamp;
        }
    }
}
