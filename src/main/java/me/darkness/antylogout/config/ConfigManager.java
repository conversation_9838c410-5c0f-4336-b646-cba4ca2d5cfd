package me.darkness.antylogout.config;

import java.util.List;
import me.darkness.antylogout.Main;
import me.darkness.antylogout.region.RegionProtectionType;
import me.darkness.antylogout.utils.ChatFormatter;
import org.bukkit.configuration.file.FileConfiguration;

public class ConfigManager {
    private final Main plugin;
    private FileConfiguration config;

    public ConfigManager(Main plugin) {
        this.plugin = plugin;
        plugin.saveDefaultConfig();
        this.config = plugin.getConfig();
    }

    public void reload() {
        this.plugin.reloadConfig();
        this.config = this.plugin.getConfig();
    }

    public int getCombatDuration() {
        return this.config.getInt("antylogout.time", 30);
    }

    public List<String> getCombatStartMessages() {
        return this.config.getStringList("antylogout.start");
    }

    public List<String> getCombatEndMessages() {
        return this.config.getStringList("antylogout.end");
    }

    public String getLogoutDuringCombatMessage() {
        return ChatFormatter.color(this.config.getString("messages.logout-during-fight", "&c%player% logged out during combat!"));
    }

    public String getPluginDisabledMessage() {
        return ChatFormatter.color(this.config.getString("messages.antylogout-disabled", "&cAntylogout has been disabled!"));
    }

    public String getPluginEnabledMessage() {
        return ChatFormatter.color(this.config.getString("messages.antylogout-enabled", "&aAntylogout has been enabled!"));
    }

    public String getConfigReloadedMessage() {
        return ChatFormatter.color(this.config.getString("messages.config-reloaded", "&aConfiguration reloaded!"));
    }

    public String getNoPermissionMessage() {
        return ChatFormatter.color(this.config.getString("messages.no-permission", "&cError! You don't have permission for this command!"));
    }

    public String getInvalidUsageMessage() {
        return ChatFormatter.color(this.config.getString("messages.invalid-usage", "&cUsage: /adminantylogout <on|off|reload>"));
    }

    public String getUnknownOptionMessage() {
        return ChatFormatter.color(this.config.getString("messages.unknown-option", "&cUnknown option! Use: on, off, or reload"));
    }

    public String getCannotEnterRegionMessage() {
        return ChatFormatter.color(this.config.getString("messages.cannot-enter-region", "&cYou cannot enter this region during combat!"));
    }

    public String getCannotUseCommandMessage() {
        return ChatFormatter.color(this.config.getString("messages.cannot-use-command", "&cYou cannot use this command during combat!"));
    }

    public String getCannotUseEnderChestMessage() {
        return ChatFormatter.color(this.config.getString("messages.cannot-use-enderchest", "&cYou cannot open ender chests during combat!"));
    }

    public String getEscapeAttemptBlockedMessage() {
        return ChatFormatter.color(this.config.getString("messages.escape-attempt-blocked", "&c&lNie możesz uciec po ataku!"));
    }

    public boolean isMobCombatEnabled() {
        return this.config.getBoolean("antylogout.settings.mobs", false);
    }

    public boolean isProjectileCombatEnabled() {
        return this.config.getBoolean("antylogout.settings.projectile", true);
    }

    public boolean isEnderPearlCombatEnabled() {
        return this.config.getBoolean("antylogout.settings.pearl", false);
    }

    public boolean isEnderChestBlocked() {
        return this.config.getBoolean("antylogout.settings.ender-chest", false);
    }

    public boolean isBossBarEnabled() {
        return this.config.getBoolean("antylogout.settings.bossbar-timer", true);
    }

    public List<String> getWhitelistedCommands() {
        return this.config.getStringList("antylogout.commands.whitelist");
    }

    public List<String> getBlockedRegions() {
        return this.config.getStringList("antylogout.regions.blocked");
    }

    public RegionProtectionType getRegionProtectionType() {
        String type = this.config.getString("antylogout.regions.protection-type", "BARRIER");
        return RegionProtectionType.fromString(type);
    }
}
